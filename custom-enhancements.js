// 大田的邮局 - 自定义增强脚本

(function() {
    'use strict';

    // 等待DOM加载完成
    function waitForDOM() {
        return new Promise(resolve => {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', resolve);
            } else {
                resolve();
            }
        });
    }

    // 等待Vue应用加载完成
    function waitForApp() {
        return new Promise(resolve => {
            const checkApp = () => {
                const app = document.querySelector('#app');
                if (app && app.children.length > 0) {
                    resolve();
                } else {
                    setTimeout(checkApp, 100);
                }
            };
            checkApp();
        });
    }

    // 移除GitHub相关链接
    function removeGitHubLinks() {
        const selectors = [
            'a[href*="github.com"]',
            'a[href*="github"]',
            '[href*="github.com"]',
            '[href*="github"]',
            'a[title*="GitHub"]',
            'a[title*="github"]',
            '.github-link',
            '.github-corner'
        ];

        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                el.style.display = 'none';
                el.remove();
            });
        });

        // 检查文本内容包含GitHub的元素
        const allElements = document.querySelectorAll('*');
        allElements.forEach(el => {
            if (el.textContent && el.textContent.toLowerCase().includes('github')) {
                const parent = el.parentElement;
                if (parent && (el.tagName === 'A' || parent.tagName === 'A')) {
                    el.style.display = 'none';
                    el.remove();
                }
            }
        });
    }

    // 添加动画类
    function addAnimationClasses() {
        // 为卡片添加浮动动画
        const cards = document.querySelectorAll('.n-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('float');
                card.style.animationDelay = `${index * 0.2}s`;
            }, index * 100);
        });

        // 为按钮添加脉冲效果
        const primaryButtons = document.querySelectorAll('.n-button--primary-type');
        primaryButtons.forEach(button => {
            button.addEventListener('mouseenter', () => {
                button.classList.add('glow');
            });
            button.addEventListener('mouseleave', () => {
                button.classList.remove('glow');
            });
        });
    }

    // 添加粒子背景效果
    function createParticleBackground() {
        const canvas = document.createElement('canvas');
        canvas.style.position = 'fixed';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        canvas.style.pointerEvents = 'none';
        canvas.style.zIndex = '-1';
        canvas.style.opacity = '0.3';
        document.body.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        let particles = [];

        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }

        function createParticle() {
            return {
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                size: Math.random() * 2 + 1,
                opacity: Math.random() * 0.5 + 0.2
            };
        }

        function initParticles() {
            particles = [];
            for (let i = 0; i < 50; i++) {
                particles.push(createParticle());
            }
        }

        function updateParticles() {
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;

                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
            });
        }

        function drawParticles() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            particles.forEach(particle => {
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
                ctx.fill();
            });

            // 连接近距离的粒子
            particles.forEach((particle1, i) => {
                particles.slice(i + 1).forEach(particle2 => {
                    const dx = particle1.x - particle2.x;
                    const dy = particle1.y - particle2.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < 100) {
                        ctx.beginPath();
                        ctx.moveTo(particle1.x, particle1.y);
                        ctx.lineTo(particle2.x, particle2.y);
                        ctx.strokeStyle = `rgba(255, 255, 255, ${0.1 * (1 - distance / 100)})`;
                        ctx.lineWidth = 1;
                        ctx.stroke();
                    }
                });
            });
        }

        function animate() {
            updateParticles();
            drawParticles();
            requestAnimationFrame(animate);
        }

        resizeCanvas();
        initParticles();
        animate();

        window.addEventListener('resize', () => {
            resizeCanvas();
            initParticles();
        });
    }

    // 添加页面切换动画
    function addPageTransitions() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1) { // Element node
                            node.style.opacity = '0';
                            node.style.transform = 'translateY(20px)';
                            
                            setTimeout(() => {
                                node.style.transition = 'all 0.5s ease';
                                node.style.opacity = '1';
                                node.style.transform = 'translateY(0)';
                            }, 50);
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 添加鼠标跟随效果
    function addMouseFollowEffect() {
        const cursor = document.createElement('div');
        cursor.style.position = 'fixed';
        cursor.style.width = '20px';
        cursor.style.height = '20px';
        cursor.style.borderRadius = '50%';
        cursor.style.background = 'radial-gradient(circle, rgba(102, 126, 234, 0.3), transparent)';
        cursor.style.pointerEvents = 'none';
        cursor.style.zIndex = '9999';
        cursor.style.transition = 'transform 0.1s ease';
        document.body.appendChild(cursor);

        document.addEventListener('mousemove', (e) => {
            cursor.style.left = e.clientX - 10 + 'px';
            cursor.style.top = e.clientY - 10 + 'px';
        });

        document.addEventListener('mousedown', () => {
            cursor.style.transform = 'scale(1.5)';
        });

        document.addEventListener('mouseup', () => {
            cursor.style.transform = 'scale(1)';
        });
    }

    // 主初始化函数
    async function init() {
        await waitForDOM();
        await waitForApp();

        // 延迟执行以确保Vue组件完全渲染
        setTimeout(() => {
            removeGitHubLinks();
            addAnimationClasses();
            createParticleBackground();
            addPageTransitions();
            addMouseFollowEffect();

            // 定期检查并移除GitHub链接
            setInterval(removeGitHubLinks, 2000);
        }, 1000);
    }

    // 启动初始化
    init();

})();
