# 大田的邮局

一个采用沉稳简约设计风格的临时邮箱前端界面。

## 设计特色

### 🎨 视觉设计
- **简约配色**: 采用优雅的蓝灰色系，沉稳而不失现代感
- **渐变背景**: 简约的双色渐变，营造舒适的视觉体验
- **半透明卡片**: 现代化的毛玻璃效果，层次分明
- **深色模式**: 完整支持系统深色模式，自动适配

### ✨ 交互体验
- **柔和动画**: 所有交互都有平滑的过渡动画
- **悬停反馈**: 按钮和卡片的悬停效果，提升用户体验
- **响应式设计**: 完美适配桌面端和移动端
- **粒子背景**: 简约的粒子连线效果，增加视觉层次

### 🚀 技术特性
- **性能优化**: 使用CSS3硬件加速，流畅的动画效果
- **兼容性**: 支持现代浏览器，包括移动端Safari和Chrome
- **无障碍**: 保持良好的对比度和可读性
- **渐进增强**: 即使JavaScript禁用也能正常使用

## 配色方案

### 浅色模式
- **主背景**: `#f5f7fa` → `#c3cfe2` 渐变
- **主色调**: `#3498db` (优雅蓝)
- **文字颜色**: `#2c3e50` (深蓝灰)
- **卡片背景**: `rgba(255, 255, 255, 0.95)` (半透明白)

### 深色模式
- **主背景**: `#2c3e50` → `#34495e` 渐变
- **主色调**: `#3498db` (保持一致)
- **文字颜色**: `#ecf0f1` (浅灰白)
- **卡片背景**: `rgba(52, 73, 94, 0.95)` (半透明深蓝灰)

## 功能保持

✅ **完全保留原有功能**
- 临时邮箱生成
- 邮件接收和查看
- 邮件管理
- 所有API调用
- 原有的业务逻辑

❌ **移除内容**
- 原作者GitHub链接
- 过于鲜艳的配色
- 过度的动画效果

## 部署方式

### Cloudflare Pages (推荐)
1. 将项目上传到Git仓库
2. 在Cloudflare Pages中连接仓库
3. 设置构建配置为静态站点
4. 部署完成

### 其他静态托管
- Vercel
- Netlify
- GitHub Pages
- 任何支持静态文件的服务器

## 本地预览

```bash
# 使用Python启动本地服务器
python3 -m http.server 8080

# 或使用Node.js
npx serve .

# 然后访问 http://localhost:8080
```

## 自定义修改

如需进一步自定义：

1. **修改配色**: 编辑 `custom-styles.css` 中的颜色变量
2. **调整动画**: 修改CSS中的 `transition` 和 `animation` 属性
3. **粒子效果**: 在 `custom-enhancements.js` 中调整粒子数量和样式
4. **响应式**: 修改CSS中的媒体查询断点

## 浏览器支持

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ iOS Safari 12+
- ✅ Android Chrome 60+

## 许可证

基于原项目进行样式美化，保持原有功能不变。仅用于学习和个人使用。

---

**大田的邮局** - 简约而不简单的临时邮箱服务
