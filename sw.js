if(!self.define){let e,s={};const t=(t,o)=>(t=new URL(t+".js",o).href,s[t]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=t,e.onload=s,document.head.appendChild(e)}else e=t,importScripts(t),s()})).then((()=>{let e=s[t];if(!e)throw new Error(`Module ${t} didn’t register its module`);return e})));self.define=(o,i)=>{const n=e||("document"in self?document.currentScript.src:"")||location.href;if(s[n])return;let r={};const f=e=>t(e,n),l={module:{uri:n},exports:r,require:f};s[n]=Promise.all(o.map((e=>l[e]||f(e)))).then((e=>(i(...e),r)))}}define(["./workbox-1ec72418"],(function(e){"use strict";self.addEventListener("message",(e=>{e.data&&"SKIP_WAITING"===e.data.type&&self.skipWaiting()})),e.precacheAndRoute([{url:"logo.png",revision:"f13f7a9b0f4adf6b653f5320465b8f09"},{url:"manifest.webmanifest",revision:"d7f3d7a23d0b9f65aa265512e402e409"}],{}),e.cleanupOutdatedCaches(),self.__WB_DISABLE_DEV_LOGS=!0}));
