/* 大田的邮局 - 自定义样式 */

/* 全局样式重置和基础设置 */
* {
  box-sizing: border-box;
}

/* 隐藏GitHub相关链接 */
a[href*="github.com"],
a[href*="github"],
[href*="github.com"],
[href*="github"] {
  display: none !important;
}

/* 主体背景 - 简约渐变 */
body {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #2c3e50;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ecf0f1;
  }
}

/* 应用容器 */
#app {
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片样式增强 */
.n-card {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden !important;
}

.n-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
}

/* 深色模式卡片 */
@media (prefers-color-scheme: dark) {
  .n-card {
    background: rgba(52, 73, 94, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
  }

  .n-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4) !important;
  }
}

/* 按钮样式增强 */
.n-button {
  border-radius: 8px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-weight: 500 !important;
  position: relative !important;
  overflow: hidden !important;
}

.n-button--primary-type {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
  border: none !important;
  color: white !important;
}

.n-button--primary-type:hover {
  background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 16px rgba(52, 152, 219, 0.3) !important;
}

.n-button--secondary-type {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: #2c3e50 !important;
}

.n-button--secondary-type:hover {
  background: rgba(255, 255, 255, 1) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* 深色模式按钮 */
@media (prefers-color-scheme: dark) {
  .n-button--secondary-type {
    background: rgba(52, 73, 94, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ecf0f1 !important;
  }

  .n-button--secondary-type:hover {
    background: rgba(52, 73, 94, 1) !important;
  }
}

/* 按钮点击波纹效果 */
.n-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.6s, height 0.6s;
  transform: translate(-50%, -50%);
  z-index: 0;
}

.n-button:active::before {
  width: 300px;
  height: 300px;
}

/* 输入框样式增强 */
.n-input {
  border-radius: 8px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.n-input:focus-within {
  background: rgba(255, 255, 255, 1) !important;
  border-color: #3498db !important;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1) !important;
}

/* 深色模式输入框 */
@media (prefers-color-scheme: dark) {
  .n-input {
    background: rgba(52, 73, 94, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ecf0f1 !important;
  }

  .n-input:focus-within {
    background: rgba(52, 73, 94, 1) !important;
    border-color: #3498db !important;
  }
}

/* 表格样式增强 */
.n-data-table {
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
}

.n-data-table-th {
  background: rgba(248, 249, 250, 0.95) !important;
  color: #2c3e50 !important;
  font-weight: 600 !important;
}

.n-data-table-tr:hover {
  background: rgba(52, 152, 219, 0.05) !important;
}

/* 深色模式表格 */
@media (prefers-color-scheme: dark) {
  .n-data-table {
    background: rgba(52, 73, 94, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .n-data-table-th {
    background: rgba(44, 62, 80, 0.95) !important;
    color: #ecf0f1 !important;
  }

  .n-data-table-tr:hover {
    background: rgba(52, 152, 219, 0.1) !important;
  }
}

/* 警告框样式增强 */
.n-alert {
  border-radius: 8px !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

/* 布局容器样式 */
.n-layout {
  background: transparent !important;
}

.n-layout-header {
  background: rgba(255, 255, 255, 0.95) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
  backdrop-filter: blur(10px) !important;
}

/* 深色模式布局 */
@media (prefers-color-scheme: dark) {
  .n-alert {
    background: rgba(52, 73, 94, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .n-layout-header {
    background: rgba(52, 73, 94, 0.95) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  }
}

/* 加载动画增强 */
.n-spin {
  color: #3498db !important;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.pulse {
  animation: pulse 2s infinite;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float {
  animation: float 3s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .n-card {
    margin: 10px !important;
    border-radius: 16px !important;
  }
  
  .n-button {
    border-radius: 10px !important;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 特殊效果 - 光晕 */
.glow {
  box-shadow: 0 0 20px rgba(52, 152, 219, 0.3) !important;
}

/* 标题样式增强 */
h1, h2, h3, h4, h5, h6 {
  color: #2c3e50 !important;
  font-weight: 600 !important;
}

/* 深色模式标题 */
@media (prefers-color-scheme: dark) {
  h1, h2, h3, h4, h5, h6 {
    color: #ecf0f1 !important;
  }
}

/* 邮件列表项动画 */
.n-data-table-tr {
  transition: all 0.3s ease !important;
}

.n-data-table-tr:hover {
  transform: translateX(2px) !important;
  background: rgba(52, 152, 219, 0.05) !important;
}

/* 模态框样式增强 */
.n-modal {
  backdrop-filter: blur(20px) !important;
}

.n-modal .n-card {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
}

/* 选择器样式增强 */
.n-select {
  border-radius: 12px !important;
}

.n-select-menu {
  backdrop-filter: blur(20px) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 12px !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* 通知样式增强 */
.n-message {
  backdrop-filter: blur(20px) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 12px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* 加载状态样式 */
.n-spin-container {
  backdrop-filter: blur(10px) !important;
}

/* 标签页样式增强 */
.n-tabs {
  background: transparent !important;
}

.n-tabs-tab {
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.n-tabs-tab:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.n-tabs-tab--active {
  background: rgba(102, 126, 234, 0.2) !important;
  color: #667eea !important;
}

/* 进度条样式 */
.n-progress {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 10px !important;
}

.n-progress-graph-line-fill {
  background: linear-gradient(90deg, #667eea, #764ba2) !important;
}

/* 开关样式增强 */
.n-switch {
  transition: all 0.3s ease !important;
}

.n-switch:hover {
  transform: scale(1.05) !important;
}

/* 分页器样式 */
.n-pagination {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 12px !important;
  padding: 8px !important;
  backdrop-filter: blur(10px) !important;
}

.n-pagination-item {
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.n-pagination-item:hover {
  background: rgba(102, 126, 234, 0.2) !important;
}

/* 工具提示样式 */
.n-tooltip {
  backdrop-filter: blur(20px) !important;
  background: rgba(0, 0, 0, 0.8) !important;
  border-radius: 8px !important;
}

/* 抽屉样式增强 */
.n-drawer {
  backdrop-filter: blur(20px) !important;
}

.n-drawer-content {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
}

/* 特殊动画效果 */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0,-30px,0); }
  70% { transform: translate3d(0,-15px,0); }
  90% { transform: translate3d(0,-4px,0); }
}

.bounce {
  animation: bounce 1s ease infinite;
}

/* 旋转动画 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.rotate {
  animation: rotate 2s linear infinite;
}

/* 缩放脉冲动画 */
@keyframes scalePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.scale-pulse {
  animation: scalePulse 2s ease-in-out infinite;
}
