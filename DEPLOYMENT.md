# 大田的邮局 - 部署指南

## 项目概述
这是一个经过美化的临时邮箱前端界面，采用沉稳简约的设计风格，具有以下特性：
- 简约优雅的渐变背景
- 现代化的半透明卡片设计
- 柔和的交互动画效果
- 完整的深色模式支持
- 响应式设计
- 简约的粒子背景效果
- 移除了原作者的GitHub链接
- 用户友好的界面设计

## 修改内容
1. **标题更改**: 将"Temp Email"改为"大田的邮局"
2. **样式重设计**: 采用沉稳简约的配色方案
   - 主色调：优雅的蓝灰色系 (#3498db, #2c3e50)
   - 背景：简约的渐变背景
   - 卡片：半透明白色/深色卡片设计
3. **深色模式支持**: 完整的深色模式适配
4. **动效优化**: 柔和的交互动画，不过分张扬
5. **用户体验**: 注重易用性和可读性
6. **GitHub链接移除**: 通过CSS和JavaScript移除了原作者的GitHub相关链接

## 部署到Cloudflare Pages

### 方法一：通过Git仓库部署（推荐）

1. **准备Git仓库**
   ```bash
   git init
   git add .
   git commit -m "Initial commit: 大田的邮局"
   git branch -M main
   git remote add origin <your-repo-url>
   git push -u origin main
   ```

2. **在Cloudflare Pages中创建项目**
   - 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
   - 进入 Pages 部分
   - 点击 "Create a project"
   - 选择 "Connect to Git"
   - 选择你的Git提供商（GitHub/GitLab等）
   - 选择包含项目的仓库

3. **配置构建设置**
   - Framework preset: `None`
   - Build command: 留空（因为这是静态文件）
   - Build output directory: `/`
   - Root directory: `/`

4. **部署**
   - 点击 "Save and Deploy"
   - 等待部署完成

### 方法二：直接上传文件

1. **在Cloudflare Pages中创建项目**
   - 登录 Cloudflare Dashboard
   - 进入 Pages 部分
   - 点击 "Create a project"
   - 选择 "Upload assets"

2. **上传文件**
   - 将所有项目文件打包成ZIP
   - 上传ZIP文件
   - 等待部署完成

## 文件结构
```
/
├── index.html              # 主HTML文件（已修改标题）
├── manifest.webmanifest    # PWA配置文件（已修改应用名称）
├── custom-styles.css       # 自定义样式文件
├── custom-enhancements.js  # 自定义JavaScript增强
├── assets/                 # 原始资源文件
├── logo.png               # 应用图标
├── favicon.ico            # 网站图标
└── 其他原始文件...
```

## 自定义功能说明

### CSS样式增强 (custom-styles.css)
- 简约优雅的渐变背景
- 半透明卡片设计
- 沉稳的按钮样式
- 柔和的悬停动画效果
- 完整的深色模式支持
- 响应式设计

### JavaScript增强 (custom-enhancements.js)
- 简约的粒子背景效果
- GitHub链接自动移除
- 柔和的页面切换动画
- 轻微的鼠标跟随效果
- 动态类名添加

## 注意事项

1. **功能保持**: 所有原始功能都被保留，只修改了视觉样式
2. **性能优化**: 动画使用CSS3和requestAnimationFrame优化性能
3. **兼容性**: 支持现代浏览器，包括移动端
4. **维护**: 定期检查并移除可能出现的GitHub链接

## 自定义配置

如需进一步自定义，可以修改以下文件：
- `custom-styles.css`: 调整颜色、动画、布局
- `custom-enhancements.js`: 修改交互效果、动画参数
- `index.html`: 修改页面标题、meta信息

## 故障排除

如果遇到问题：
1. 检查浏览器控制台是否有错误
2. 确保所有文件路径正确
3. 验证CSS和JavaScript语法
4. 检查Cloudflare Pages的构建日志

## 联系支持

如需技术支持或有问题，请检查：
- Cloudflare Pages文档
- 浏览器开发者工具
- 网络连接状态
